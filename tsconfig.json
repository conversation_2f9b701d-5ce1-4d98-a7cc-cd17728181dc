{
	"compilerOptions": {
		"target": "esnext",
		"module": "esnext",
		"moduleResolution": "node",
		"lib": ["DOM", "ESNEXT"],
		"jsx": "preserve",
		"forceConsistentCasingInFileNames": true, // 禁止对同一文件使用大小写不一致的引用
		"allowSyntheticDefaultImports": true, // 允许合成默认导入
		"strictFunctionTypes": false,
		"baseUrl": ".",
		"allowJs": true,
		"sourceMap": true,
		"esModuleInterop": true, // https://www.jianshu.com/p/fe303e059a29
		"resolveJsonModule": true,
		"noUnusedLocals": false,
		"noUnusedParameters": false,
		"experimentalDecorators": true,
		"noImplicitAny": false,
		"skipLibCheck": true,
		"removeComments": true, // 编译时候是否移除注释
		"paths": {
			"@/*": ["./src/*"]
		}
	},
	"include": [
		"./src/**/*.ts",
		"./src/**/*.d.ts",
		"./src/*.d.ts",
		"./src/**/*.tsx",
		"./src/**/*.jsx",
		"./src/**/*.vue",
		"./src/**/*.js",
		"./app-env/global.d.ts",
		"app-env/local-config.ts",
		"tailwind.config.js"
	],
	"exclude": ["node_modules", "dist", "**/*.js", "**/*.md", "./src/**/*.md"]
}

image: registry-vpc.cn-hangzhou.aliyuncs.com/mc-public/node:14.17.3
stages:
  - build

before_script:
  - cp /frontEndScripts/init.sh /init.sh
  - chmod +x /init.sh

build_pack:
  stage: build
  variables:
    BUILD_ART_PATH: dist
    BUILD_CMD: |-
      master: npm run build
      dev*: npm run build:dev
      test*: npm run build:test
  tags:
    - k8s-front
  artifacts:
    expire_in: 7 days
    name: '$CI_PROJECT_NAME-$CI_COMMIT_BRANCH'
    paths:
      - project.zip
  script:
    - cat /init.sh
    - /init.sh

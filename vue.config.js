const { defineConfig } = require('@vue/cli-service');
const { VantResolver } = require('unplugin-vue-components/resolvers');
const ComponentsPlugin = require('unplugin-vue-components/webpack');
const webpack = require('webpack');
const path = require('path');
const getAppConfig = require('./app-env/config.js');
const vConsolePlugin = require('vconsole-webpack-plugin');
const ip = require('ip');
const resolve = dir => path.join(__dirname, dir); // 路径

const isProd = process.argv.includes('--isProd');
const isDev = process.argv.includes('--isDev');
const isLocal = process.argv.includes('--isLocal');
const isTest = process.argv.includes('--isTest');

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);
const port = 5550;

// console.log('isProd, isDev, isLocal, isTest', isProd, isDev, isLocal, isTest);

module.exports = defineConfig({
	lintOnSave: true,
	publicPath: './' + isLocal ? '' : 'dist',
	productionSourceMap: false,
	outputDir: 'dist',
	pages: require(resolve('page.json')),
	devServer: {
		port,
		headers: {
			'Access-Control-Allow-Origin': '*'
		},
		client: {
			overlay: false,
			webSocketURL: `wss://${ip.address().replace(/\./g, '-')}-${port}.local.mucang.cn/ws`
		}
	},
	css: {
		loaderOptions: {
			less: {
				lessOptions: {
					javascriptEnabled: true
				}
			}
		}
	},
	transpileDependencies: true,

	chainWebpack: config => {
		// 移除 prefetch 插件
		config.plugins.delete('prefetch');
		// 不同环境对应不同的map
		if (isLocal || isDev) {
			config.devtool('source-map');
		} else if (isTest) {
			config.devtool('cheap-source-map');
		}

		config.resolve.alias.set('@', resolve('src'));
		config.name = appConfig.enName;
		config.optimization.chunkIds = 'named';
		// 依赖包拆分和代码拆分
		config.optimization.splitChunks({
			chunks: 'all',
			// 按需加载的代码块（vendor-chunk）并行请求的数量小于或等于10个
			maxAsyncRequests: 10,
			maxInitialRequests: 50, // 最大初始化请求
			minSize: 100000, // 依赖包超过100kb将被单独打包
			cacheGroups: {
				libs: {
					name(module) {
						//  拆分每个 npm 包
						let packageName = 'null' + Math.floor(Math.random() * 10);
						let npmName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
						if (npmName && npmName[1]) {
							packageName = npmName[1];
						}
						return `npm.${packageName.replace('@', '')}`;
					},
					test: /[\\/]node_modules[\\/]/,
					priority: 10,
					chunks: 'initial' // only package third parties that are initially dependent
				},
				appModule: {
					name(module) {
						let packageName = module.context.match(/[\\/]application[\\/](.*?)([\\/]|$)/);
						if (packageName) {
							packageName = packageName[1];
						}
						return `application.${packageName}`;
					},
					test: /[\\/]src[\\/]_?application(.*)/,
					priority: 8,
					enforce: true
				}
			}
		});
	},
	configureWebpack: config => {
		// 生产环境下去掉输出和debugger
		if (isProd) {
			config.optimization.minimizer[0].options.minimizer.options.compress = Object.assign(
				config.optimization.minimizer[0].options.minimizer.options.compress,
				{
					drop_console: true,
					drop_debugger: true
				}
			);
		}

		config.plugins.push(
			// 定义全局变量
			new webpack.DefinePlugin({
				APP: JSON.stringify(appConfig)
			})
		);

		config.plugins.push(
			// vant 组件按需引入配置
			ComponentsPlugin({
				resolvers: [VantResolver()]
			})
		);
		config.watchOptions = {
			aggregateTimeout: 500
		};

		config.plugins.push(
			new vConsolePlugin({
				filter: [],
				enable: !isProd
			})
		);
	}
});

export default {
	getDomId(id) {
		return id.match(/\./gi) ? document.querySelectorAll(id)[0] : document.getElementById(id);
	},

	/**
	 * 加载中
	 * @param {*} width 圆圈大小
	 * @param {*} frontColor 圆圈前景色
	 * @param {*} bgColor 圆圈背景色
	 * @param {*} maskColor 遮罩背景色
	 * @param {*} domId 外层自定义 dom id
	 */
	loading(
		width = 40,
		frontColor = 'rgba(255, 101, 1, 0.2)',
		bgColor = '#F37D0F',
		maskColor = 'transparent',
		domId = 'fixedLoading'
	) {
		let keyframe;
		let $fixedLoading;
		const div = document.createElement('div');

		domId = domId || 'fixedLoading';
		$fixedLoading = this.getDomId('j-' + domId);
		this.toastTimer = this.toastTimer || null;

		if (!$fixedLoading || $fixedLoading.style.display !== 'block') {
			width = width || 40;
			frontColor = frontColor || 'rgba(255, 101, 1, 0.2)';
			bgColor = bgColor || '#F37D0F';
			maskColor = maskColor || 'transparent';

			keyframe =
				'<style>@-webkit-keyframes ldinggif{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg);}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg);}}@keyframes ldinggif{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg);}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg);}}' +
				'.ldingcon{display:block;border:2px solid ' +
				frontColor +
				';border-left-color:' +
				bgColor +
				';-webkit-transform:translateZ(0);-ms-transform:translateZ(0);transform:translateZ(0);-webkit-animation:ldinggif 1.1s infinite linear;animation:ldinggif 1.1s infinite linear;}.ldingcon,.ldingcon:after{border-radius:50%;width:' +
				width +
				'px;height:' +
				width +
				'px;}}</style>';

			div.id = 'j-' + domId;
			div.setAttribute(
				'style',
				'z-index: 1001; display: none; position: fixed;  left: 0; width: 100%; color: #fff; text-align: center; background: ' +
					maskColor +
					'; top: 0; height: 100%;'
			);
			div.innerHTML =
				'<div class="con" style="display: inline-block; max-width: 300px; margin: 0 auto; padding: 14px 30px; position: absolute; transform: translate3D(-50%, -50%, 0); -webkit-transform: translate3D(-50%, -50%, 0); top: 50%; left: 50%; border-radius: 4px; font-size: 15px;">' +
				keyframe +
				'<span class="ldingcon"></span></div>';
			document.body.appendChild(div);
			$fixedLoading = this.getDomId('j-' + domId);
		}

		$fixedLoading.style.display = 'block';
	},
	loadingHide(domId = 'fixedLoading') {
		const $loading = this.getDomId('j-' + (domId || 'fixedLoading'));

		if ($loading) {
			$loading.parentNode.removeChild($loading);
		}
	},

	toast(msg, timer = 1000) {
		let $fixedTip = this.getDomId('j-fixedTip');
		const div = document.createElement('div');

		this.toastTimer = this.toastTimer || null;

		if ($fixedTip) {
			if ($fixedTip.style.display !== 'block') {
				$fixedTip.style.display = 'none';
				$fixedTip.childNodes[0].innerHTML = msg;
			}
		} else {
			div.id = 'j-fixedTip';
			div.setAttribute(
				'style',
				'z-index: 1001; display: none; position: fixed;  left: 0; width: 100%; color: #fff; text-align: center; top: 50%; transform: translate3D(0, -50%, 0); -webkit-transform: translate3D(0, -50%, 0);'
			);
			div.innerHTML =
				'<div class="con" style="display: inline-block; max-width: 300px; margin: 0 auto; padding: 14px 30px; background: rgba(0, 0, 0, 0.8); border-radius: 4px; font-size: 15px;">' +
				msg +
				'</div>';
			document.body.appendChild(div);
			$fixedTip = this.getDomId('j-fixedTip');
		}

		$fixedTip.style.display = 'block';

		if (!timer || timer !== -1) {
			clearTimeout(this.toastTimer);
			this.toastTimer = setTimeout(function () {
				$fixedTip.style.display = 'none';
			}, timer || 2000);
		}
	},
	toastHide() {
		const $loading = this.getDomId('j-fixedTip');

		if ($loading) {
			$loading.parentNode.removeChild($loading);
		}
	}
};

import { GuideLocEnum, PullSuccessEnum } from '@/utils/hooks/abtest/constants';

export interface StateModel {
	// 二次确认
	reconfirm: boolean;
	reconfirmInfo: ReconfirmModel;
	// 挽留弹窗
	newUserImg: string;
	newUserUrl: string;
	// 是否禁止一键登录
	isBanLogin: boolean;
	// 问卷页面提示是否展示
	questionnaireTipVisible: boolean;
}

// 二次确认
export interface ReconfirmModel {
	content: string;
	cancel: string;
	submit: string;
}

// 挽留弹窗
export interface NewUserModel {
	cancel_url: string;
	cancel_img: string;
	submit_url: string;
	submit_img: string;
}

// 驾照类型弹窗，是否摩托车
export interface MotoModel {
	code: string;
}

export interface AccessFineLocationModel {
	key: string;
	strategy: GuideLocEnum;
}

// 配置项参数
export interface AbtestResponse<T> {
	cityRatios: string;
	key: string;
	params: T;
	pullSuccess: PullSuccessEnum;
}

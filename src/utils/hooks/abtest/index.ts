import { PostAbtestStore } from '@/store/awd/h5';
import { reactive } from 'vue';
import Utils from '@/utils/utils';
import { TypeEnum } from '@/components/dialog-result/constants';
import {
	AbtestResponse,
	AccessFineLocationModel,
	MotoModel,
	NewUserModel,
	ReconfirmModel,
	StateModel
} from '@/utils/hooks/abtest/types';
import { AbtestEnum, DEFAULT_GUIDE_LOC, PullSuccessEnum } from '@/utils/hooks/abtest/constants';

// 远程配置
export const useRemoteSettingStore = () => {
	const state = reactive<StateModel>({
		// 二次确认
		reconfirm: false,
		reconfirmInfo: null,
		// 挽留弹窗
		newUserImg: null,
		newUserUrl: null,
		// 是否禁止一键登录
		isBanLogin: false,
		// 问卷页面提示是否展示
		questionnaireTipVisible: false
	});

	const methods = {
		// 二次确认
		async getReconfirmDialog(): Promise<PullSuccessEnum> {
			const cityCode = (await Utils.getSystemBaseInfo())._userCity;

			return new Promise(resolve => {
				PostAbtestStore({
					cityCode,
					key: AbtestEnum.RECONFIRM_DIALOD
				})
					.then((data: AbtestResponse<ReconfirmModel>) => {
						if (data) {
							state.reconfirm = data.pullSuccess === PullSuccessEnum.SUCCESS;
							state.reconfirmInfo = data.params;
							resolve(data.pullSuccess);
						}
					})
					.catch(() => {
						state.reconfirm = false;
						resolve(PullSuccessEnum.DEFAULT);
					})
			});
		},
		// 挽留弹窗
		getNewUserDialog(userAreaCode: string, type: TypeEnum): Promise<boolean> {
			// userAreaCode 取的表单code，如果表单没有值则取基础参数

			return new Promise(resolve => {
				PostAbtestStore({
					key: AbtestEnum.NEW_USER_DIALOG,
					cityCode: userAreaCode
				})
					.then((data: AbtestResponse<NewUserModel>) => {
						if (data.pullSuccess === PullSuccessEnum.SUCCESS) {
							if (type === TypeEnum.SUBMIT) {
								state.newUserImg = data.params.submit_img;
								state.newUserUrl = data.params.submit_url;
							} else {
								state.newUserImg = data.params.cancel_img;
								state.newUserUrl = data.params.cancel_url;
							}

							resolve(true);
						} else {
							resolve(false);
						}
					})
					.catch(() => {
						resolve(false);
					});
			});
		},
		// 驾照类型弹窗，是否摩托车
		getMotoDialog(cityCode: string): Promise<boolean> {
			return new Promise(resolve => {
				PostAbtestStore({
					cityCode,
					// cityCode: '420111',
					key: AbtestEnum.MOTO_GUIDE
				})
					.then((data: AbtestResponse<MotoModel>) => {
						const show = data.params?.code?.includes(cityCode.slice(0, 4));
						console.log('moto show: ', show);

						resolve(show);
					})
					.catch(() => {
						resolve(false);
					});
			});
		},
		// 是否禁止一键登录
		async getDisabledQuicklyLogin(): Promise<PullSuccessEnum> {
			const cityCode = (await Utils.getSystemBaseInfo())._userCity;

			return new Promise(resolve => {
				PostAbtestStore({
					cityCode,
					key: AbtestEnum.QUICKLY_LOGIN_FORBIDEN
				})
					.then((data: AbtestResponse<void>) => {
						state.isBanLogin = data.pullSuccess === PullSuccessEnum.SUCCESS;
						resolve(data.pullSuccess);
					})
					.catch(() => {
						resolve(PullSuccessEnum.DEFAULT);
					});
			});
		},
		// 是否展示推荐弹窗
		getRecommendDialog(cityCode: string): Promise<boolean> {
			return new Promise(resolve => {
				PostAbtestStore({
					cityCode,
					key: AbtestEnum.NEW_USER_1V1
				})
					.then((data: AbtestResponse<void>) => {
						resolve(data.pullSuccess === PullSuccessEnum.SUCCESS);
					})
					.catch(() => {
						resolve(false);
					});
			});
		},
		// 问卷页面提示是否展示
		async getQuestionnaireTipShow(): Promise<void> {
			const cityCode = (await Utils.getSystemBaseInfo())._userCity;

			return new Promise(resolve => {
				PostAbtestStore({
					cityCode,
					key: AbtestEnum.INFORMATION_COLLECT_CANCEL_TIP
				})
					.then((data: AbtestResponse<void>) => {
						state.questionnaireTipVisible = data.pullSuccess === PullSuccessEnum.SUCCESS;
						resolve();
					})
					.catch(() => {
						state.questionnaireTipVisible = false;
						resolve();
					});
			});
		},
		// 获取定位策略
		getLocationStrategyKey(): Promise<AccessFineLocationModel> {
			return new Promise((resolve, reject) => {
				// eslint-disable-next-line @typescript-eslint/ban-ts-comment
				// @ts-ignore
				MCProtocol['jiakao-global'].getAbTestConfig({
					callback: res => {
						if (res.success) {
							res.data[AbtestEnum.JK_GUIDE_LOC]
								? resolve(res.data[AbtestEnum.JK_GUIDE_LOC])
								: resolve(DEFAULT_GUIDE_LOC);
						} else {
							reject();
						}
					}
				});
			});
		}
	};

	return {
		state,
		...methods
	};
};

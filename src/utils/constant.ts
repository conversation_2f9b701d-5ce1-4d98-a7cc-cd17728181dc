// 预加载完成标识
export enum EnterEnquiryCodeEnum {
	IOS = '1',
	ANDROID = 1
}

// 固定跳转地址
export const MC_LOCAL_HOST_URL = '/';
// 'jiakaobaodian-66-student-driver-section/';

// 老版木仓协议支持的所有协议
// 注意：一律全部小写
export const OLDAPPFUNS = [
	'core.web.open',
	'core.web.setting',
	'core.web.back',
	'core.web.close',
	'core.web.menu',
	'core.web.requestinterceptclose',
	'core.http.get',
	'core.http.post',
	'core.user.get',
	'core.user.login',
	'core.user.logout',
	'core.native.album',
	'core.native.saveimage',
	'core.native.uploadimage',
	'core.native.uploadfile',
	'core.share.setting',
	'core.share.open',
	'core.system.getcache',
	'core.system.setcache',
	'core.system.stat',
	'core.system.version',
	'core.system.info',
	'core.system.call',
	'core.system.log',
	'core.system.toast',
	'core.system.alert',
	'core.system.getconfig',
	'core.system.confirm',
	'core.system.env',
	'core.openapp',
	'core.checkapp',
	'listener.sharechannel',
	'listener.show',
	'listener.hide',
	'listener.onwebviewclose'
];

// 定义手机号码正则
export const REG_RULE = {
	phone: APP.isOnline ? /1[3-9]\d{9}/ : /(1|9)[3-9]\d{9}/,
	name: /[\u4e00-\u9fa5]{2,4}/
};

// 检查有无精确定位权限
export const ACCESS_FINE_LOCATION = 'android.permission.ACCESS_FINE_LOCATION';

// 获取精准定位返回状态
export const ACCESS_FINE_LOCATION_STATUS = {
	// 同意授权
	success: 'granted',
	// 拒绝
	fail: 'denied'
};

// 拼接的字符串传参
export const ENTRANCE = [
	{
		key: 'isBanLogin',
		value: '_banLogin'
	}
];

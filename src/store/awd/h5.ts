import { request } from '@/utils/store';
import { MethodEnum } from '@/utils/store/type';

// 获取配置键的值
export const PostAbtestStore = params =>
	request({
		url: `${APP.domain.awd}/api/h5/v4/home-lead-advertise/abtest.htm`,
		method: MethodEnum.POST,
		params
	});

export const PostListJiaxiaoStore = params => {
	return request({
		url: `${APP.domain.awd}/api/h5/list/jiaxiao/list-jiaxiao-by-lead.htm`,
		method: MethodEnum.POST,
		params
	});
};

export const ListQuestionStore = params => {
	return request({
		url: `${APP.domain.awd}/api/web/v3/user-inquiry/list-question.htm`,
		method: MethodEnum.GET,
		params
	});
};

export const UploadLabelStore = params => {
	return request({
		url: `${APP.domain.awd}/api/h5/v4/home-lead-advertise/upload-label.htm`,
		method: MethodEnum.POST,
		params
	});
};

export const PostSimpleInfoStore = params => {
	return request({
		url: `${APP.domain.awd}/api/h5/v4/jiaxiao/get-jiaxiao-simple-info.htm`,
		method: MethodEnum.POST,
		params
	});
};

import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import Utils from '@/utils/utils';
import { trackEvent } from '@/utils/stat';
import UI from '@/utils/ui';
import { ADDRESS, IOS_ENTER_FIRST } from '@/utils/storage-key';
import HeaderComp from '@/components/headers/index.vue';
import QuestionListComp from '@/components/question-list/index.vue';
import DialogResultComp from '@/components/dialog-result/index.vue';
import { ListQuestionStore, UploadLabelStore } from '@/store/awd/h5';
import { SelectModel } from '@/components/question-list/types';
import { QuestionListResponse, StateModel } from './types';
import { EnterEnquiryCodeEnum } from '@/utils/constant';
import { UrlQueryModel } from '@/application/index/page/comp/form/types';
import { TypeEnum } from '@/components/dialog-result/constants';
import MCProtocol from '@simplex/simple-mcprotocol';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';

export default defineComponent({
	components: {
		HeaderComp,
		QuestionListComp,
		DialogResultComp
	},
	setup() {
		const remoteSettingStore = useRemoteSettingStore();

		const state = reactive<StateModel>({
			questionList: [],
			selectList: []
		});

		const components = {
			dialogResultRef: ref<InstanceType<typeof DialogResultComp>>()
		};

		// 方法
		const methods = {
			// 获取问题列表
			getListQuestion(): void {
				ListQuestionStore({ type: -1 }).then((res: QuestionListResponse) => {
					state.questionList = res.itemList || [];
				});
			},

			onQuestionSelect(data: SelectModel[]): void {
				state.selectList = data;
			},

			// 跳过
			async onSkip(): Promise<void> {
				// 显示留资弹框
				const address = Utils.store.get(ADDRESS);
				if (address && JSON.parse(address).cityCode) {
					await components.dialogResultRef.value?.show(TypeEnum.SUBMIT, JSON.parse(address).cityCode);

					MCProtocol.Core.Web.close();
				}
			},

			// 完成
			onComplete(): void {
				methods.updateQusetionLabel();
			},
			// 更新
			updateQusetionLabel() {
				UI.loading();
				const list = state.selectList.filter(item => {
					return item.optionIndexList.length > 0;
				});
				UploadLabelStore({
					list: JSON.stringify(list)
				})
					.then(() => {
						setTimeout(() => {
							methods.onSkip();
						}, 500);
					})
					.finally(() => {
						UI.loadingHide();
					});
			},
			// 点击返回事件
			async onBack() {
				const address = Utils.store.get(ADDRESS);
				if (address && JSON.parse(address).cityCode) {
					// 显示挽留弹框
					await components.dialogResultRef.value.show(TypeEnum.DETERRENT, JSON.parse(address).cityCode);

					MCProtocol.Core.Web.close();
				}
			}
		};
		onMounted(() => {
			methods.getListQuestion();
			const href = window.location.href;
			const urlQuery: UrlQueryModel = Utils.getURLParams('', href);
			if (urlQuery.postFlow === '1') {
				const iosEnterFirst = Utils.store.get(IOS_ENTER_FIRST); // ios预加载完成
				const forbidReportLog = Number(urlQuery.forbidReportLog); // android预加载完成
				if (
					(Utils.isIOS && iosEnterFirst === EnterEnquiryCodeEnum.IOS) ||
					(Utils.isAndroid && forbidReportLog !== EnterEnquiryCodeEnum.ANDROID)
				) {
					trackEvent({
						PageName: '帮我找驾校h5-标签页',
						actionName: '加载成功',
						actionType: '',
						abTest: '留资后置'
					});
				}
			} else {
				trackEvent({
					PageName: '帮我找驾校h5-标签页',
					actionName: '加载成功',
					actionType: '',
					abTest: '留资前置'
				});
			}

			remoteSettingStore.getQuestionnaireTipShow();
		});

		return {
			remoteSettingStore,
			...toRefs(state),
			...components,
			...methods
		};
	}
});

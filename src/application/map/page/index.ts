import { defineComponent, reactive, toRefs } from 'vue';
import HeaderComp from '@/components/headers/index.vue';
import MapComp from '@/components/a-map/index.vue';
import { StateModel } from './types';

export default defineComponent({
	components: {
		HeaderComp,
		MapComp
	},
	setup() {
		const state = reactive<StateModel>({});
		// 常量
		const constants = {};
		// 页面组件实例集合
		const components = {};
		// 方法
		const methods = {};

		return {
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});

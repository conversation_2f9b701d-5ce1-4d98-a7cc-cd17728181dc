import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';
import AMapInit from '@/utils/a-map';
import { EnterEnquiryCodeEnum } from '@/utils/constant';
import { trackEvent } from '@/utils/stat';
import { ADDRESS, CAR_DESC, CAR_TYPE, DURATION, IOS_ENTER_FIRST } from '@/utils/storage-key';
import HeaderComp from '@/components/headers/index.vue';
import FormComp from '@/application/index/page/comp/form/index.vue';
import { QuickLoginResponse, StateModel } from './types';
import {
	BaseParamsModel,
	CoachItemModel,
	JiaxiaoItemModel,
	RecommendModel
} from '@/application/index/page/comp/form/types';
import { PostListJiaxiaoStore } from '@/store/awd/h5';
import useRouter from '@/utils/hooks/router';
import DialogResultComp from '@/components/dialog-result/index.vue';
import DialogRecommendComp from '@/application/index/page/comp/dialog-recommend/index.vue';
import DialogPrivatePhoneComp from '@/application/index/page/comp/dialog-private-phone/index.vue';
import { SourceEnum } from '@/application/index/page/comp/form/constants';
import { RecommendReturn } from '@/application/index/page/comp/dialog-recommend/types';
import UI from '@/utils/ui';
import DialogDoubleConfirmComp from '@/application/index/page/comp/dialog-double-confirm/index.vue';
import { TypeEnum } from '@/components/dialog-result/constants';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';
import { useLocationStore } from '@/utils/hooks/location';
import { SystemInfoModel } from '@/utils/types';

import { LICENSE_OPTIONS } from '@/utils/options/license';

export default defineComponent({
	components: {
		HeaderComp,
		FormComp,
		DialogResultComp,
		DialogRecommendComp,
		DialogPrivatePhoneComp,
		DialogDoubleConfirmComp
	},
	setup() {
		const router = useRouter();

		const remoteSettingStore = useRemoteSettingStore();
		const locationStore = useLocationStore();

		const state = reactive<StateModel>({
			// 获取当前所在的城市
			userCity: '',
			// url携带的参数
			urlQuery: null
		});

		const components = {
			formRef: ref<InstanceType<typeof FormComp>>(),
			dialogResultRef: ref<InstanceType<typeof DialogResultComp>>(),
			dialogRecommendRef: ref<InstanceType<typeof DialogRecommendComp>>(),
			dialogPrivatePhoneRef: ref<InstanceType<typeof DialogPrivatePhoneComp>>(),
			dialogDoubleConfirmRef: ref<InstanceType<typeof DialogDoubleConfirmComp>>()
		};

		const methods = {
			// 正常初始化
			async init() {
				// 预加载完成
				await methods.getRemoteSettingQuery();

				methods.initEvent();
				// 获取url参数
				methods.getQuery();
				// 存储当前时间戳
				methods.getCurrentTime();

				// 回填驾照类型
				await methods.getCarType();
				// 回填地址
				await methods.getAddressInfo();

				// 获取当前所在的城市
				state.userCity = (await Utils.getSystemBaseInfo())._userCity;

				// 获取是否禁止一键登录
				await methods.getRemoteData();
				// 一键登录
				!remoteSettingStore.state.isBanLogin && methods.checkQuickLogin();
			},
			initEvent() {
				Utils.listenPage(res => {
					// 回到首页更新数据
					if (!res) {
						components.formRef.value.getStorage();
					}
				});
			},
			// 获取url参数
			getQuery() {
				const href = window.location.href;
				const params = Utils.getURLParams('', href);
				console.log('url query: ', params);

				state.urlQuery = params;
			},
			// 存储当前时间戳
			getCurrentTime() {
				if (!Utils.store.get(DURATION)) {
					Utils.store.set(DURATION, new Date().getTime());
				}
			},
			// 回填驾照类型
			getCarType(): Promise<void> {
				return new Promise(resolve => {
					let carType = 'C1';
					let carDesc = '';
					LICENSE_OPTIONS.forEach(item => {
						if (item.groupType === state.urlQuery.carStyle) {
							carType = item.list[0].carType;
						}
					});
					LICENSE_OPTIONS.forEach(item => {
						item.list.forEach(childItem => {
							if (childItem.carType === carType) {
								carDesc = childItem.carDesc;
							}
						});
					});
					if (Utils.isIOS) {
						Utils.getIosCarType(res => {
							Utils.store.set(CAR_TYPE, res.carType || carType);
							Utils.store.set(CAR_DESC, res.carDesc || carDesc);
							components.formRef.value.getStorage();
							resolve();
						});
					} else {
						Utils.store.set(CAR_TYPE, carType);
						Utils.store.set(CAR_DESC, carDesc);
						components.formRef.value.getStorage();
						resolve();
					}
				});
			},
			// 回填地址
			getAddressInfo(): Promise<void> {
				return new Promise(resolve => {
					Promise.all([locationStore.getLocation(), AMapInit(['AMap.Geocoder'])]).then(
						([systemInfo, AMap]) => {
							// console.log(systemInfo);
							if (systemInfo?._longitude && systemInfo?._latitude) {
								const geocoder = new AMap.Geocoder({
									city:
										(systemInfo as SystemInfoModel['data'])._gpsCity ||
										systemInfo._userCity ||
										(systemInfo as SystemInfoModel['data'])._ipCipy
								});
								const position = [systemInfo._longitude, systemInfo._latitude];
								geocoder.getAddress(position, (status, result) => {
									if (status === 'complete' && result.info === 'OK') {
										const address = {
											cityCode: result.regeocode.addressComponent.adcode,
											name: result.regeocode.formattedAddress,
											location: {
												lng: position[0],
												lat: position[1],
												adcode: result.regeocode.addressComponent.adcode,
												province: result.regeocode.addressComponent.province,
												city: result.regeocode.addressComponent.city,
												district: result.regeocode.addressComponent.district
											}
										};
										Utils.store.set(ADDRESS, JSON.stringify(address));
										components.formRef.value.getStorage();
										resolve();
									}
								});
							} else {
								resolve();
							}
						}
					);
				});
			},
			// 判断是否预加载完成
			getRemoteSettingQuery(): Promise<void> {
				return new Promise((resolve, reject) => {
					if (Utils.isAndroid) {
						// android预加载完成
						const forbidReportLog = Number(state.urlQuery.forbidReportLog);

						if (forbidReportLog !== EnterEnquiryCodeEnum.ANDROID) {
							resolve();
						} else {
							reject();
						}
					}

					if (Utils.isIOS) {
						Utils.updateFirstEnquiry(() => {
							// ios预加载完成
							const iosEnterFirst = Utils.store.get(IOS_ENTER_FIRST);

							if (iosEnterFirst === EnterEnquiryCodeEnum.IOS) {
								resolve();
							} else {
								reject();
							}
						});
					}
				});
			},
			// 获取是否禁止一键登录
			getRemoteData(): Promise<void> {
				return new Promise(resolve => {
					remoteSettingStore.getDisabledQuicklyLogin().then(pullSuccess => {
						trackEvent({
							PageName: '帮我找驾校页h5',
							actionName: '加载成功',
							actionType: '',
							// 进入入口
							prePageName:
								state.urlQuery.entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页',
							// 1存在地址，0不存在地址
							locationInput: components.formRef.value.address.name ? 1 : 0,
							// 0禁止登录，1允许登录，-1默认
							banLogin: [0, 1].includes(pullSuccess) ? pullSuccess : -1
						});

						resolve();
					});
				});
			},
			// 验证一键登录
			checkQuickLogin() {
				MCProtocol.Core.User.quickLoginAvailable(res => {
					if (res.success) {
						// 该协议需要打开数据流量
						MCProtocol.Core.User.login({
							from: '',
							skipAuthRealName: false,
							pageType: 'quicklogin',
							skipBindThird: true,
							callback: (res: QuickLoginResponse) => {
								components.formRef.value.setUserInfo(res);
							}
						});
					}
				});
			},
			// 留资结束
			async onSubmitEnd(params: BaseParamsModel) {
				console.log(params);

				// 是否展示推荐弹窗
				const hasRecommend = await remoteSettingStore.getRecommendDialog(params.userAreaCode);

				// 如果需要展示推荐弹窗
				if (hasRecommend) {
					// 获取推荐弹窗的列表
					const recommend = await methods.getRecommendList(params);

					// 如果有列表
					if (recommend) {
						// 格式化推荐弹窗数据
						const list = methods.formatRecommend(recommend);

						// 设置隐私号码
						components.dialogPrivatePhoneRef.value.setCallInfo(recommend, list);

						// 打开推荐弹窗
						const { interested, targets }: RecommendReturn = await components.dialogRecommendRef.value.show(
							list[0],
							recommend.listSource
						);

						if (interested) {
							// 点击感兴趣，重新留资1v1
							params.targets = targets;
							await components.formRef.value.postChev(params);
							UI.toast('提交成功');

							// 隐私号弹窗
							await components.dialogPrivatePhoneRef.value.showDialog();
						}
					}
				}

				// 二次确认弹窗
				await components.dialogDoubleConfirmRef.value.show();

				if (state.urlQuery.information === '1') {
					// 跳转到问卷页面
					methods.goQuestionnaire();
				} else {
					methods.onBack();
				}
			},

			// 获取推荐弹窗的列表
			getRecommendList(params: BaseParamsModel): Promise<RecommendModel | void> {
				return new Promise(resolve => {
					const postData = {
						driveLicenseType: params.userLicenseType,
						longitude: params.userLongitude,
						latitude: params.userLatitude,
						cityCode: params.userAreaCode
					};
					PostListJiaxiaoStore(postData).then((res: RecommendModel) => {
						// console.log(res);
						params.listSource = res.listSource || params.listSource;
						if (res.listSource === SourceEnum.JIAXIAO || res.listSource === SourceEnum.COACH) {
							resolve(res);
						} else {
							resolve();
						}
					});
				});
			},
			// 格式化推荐弹窗数据
			formatRecommend(res: RecommendModel): JiaxiaoItemModel[] | CoachItemModel[] {
				let list: JiaxiaoItemModel[] | CoachItemModel[] = [];
				if (res.listSource) {
					if (res.listSource === 'jiaxiao' || res.listSource === 'phone') {
						list = res.jiaxiaoList;
					} else if (res.listSource === 'rankCoach' || res.listSource === 'coach') {
						list = res.coachList;
					}
				}

				return list;
			},

			// 跳转到问卷页面
			goQuestionnaire() {
				Utils.store.set(ADDRESS, JSON.stringify(components.formRef.value.address));
				router.goPathJumps({
					path: 'questionnaire.html',
					coreOpenParams: {
						titleBar: false,
						orientation: 'portrait',
						button: '',
						menu: false
					}
				});
			},
			// 点击返回事件
			async onBack() {
				trackEvent({
					PageName: '帮我找驾校页h5',
					actionName: '点击“关闭”按钮',
					actionType: '',
					prePageName: state.urlQuery.entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页', // 进入入口
					numbers: {
						phoneNumberInput:
							components.formRef.value.isPhoneClick && components.formRef.value.phone !== '' ? 1 : 0, // 1修改过手机号并且不为空，0默认
						nameInput: components.formRef.value.isNameClick && components.formRef.value.name !== '' ? 1 : 0 // 1修改过称呼并且不为空，0默认
					}
				});

				const cityCode = components.formRef.value.address.cityCode || state.userCity;

				// 显示挽留弹框
				await components.dialogResultRef.value.show(TypeEnum.DETERRENT, cityCode);

				// 关闭页面
				MCProtocol.Core.Web.close();
			}
		};

		onMounted(() => {
			methods.init();
		});

		return {
			remoteSettingStore,
			...toRefs(state),
			...components,
			...methods
		};
	}
});

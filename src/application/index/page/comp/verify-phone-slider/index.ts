import { defineComponent, reactive, ref, toRefs } from 'vue';
import DialogCodeComp from '@/application/index/page/comp/verify-phone-code/index.vue';
import { EmitModel, StateModel } from './types';

export default defineComponent({
	components: {
		DialogCodeComp
	},
	setup(props, { emit }: { emit: EmitModel }) {
		const state = reactive(<StateModel>{
			visible: false // 是否显示网易图形验证码
		});

		const components = {
			dialogCodeCompRef: ref<InstanceType<typeof DialogCodeComp>>()
		};

		const methods = {
			// 显示网易图形验证码
			show() {
				state.visible = true;
				(window as any).initNECaptcha({
					captchaId: '6f92317b6e7d4f4faa77a360d65826c5',
					element: '#captcha',
					mode: 'embed',
					width: '100%',
					async onVerify(err, data) {
						if (data) {
							state.visible = false;

							emit('success', data.validate);
						}
					}
				});
			},

			// 初始化插入js
			init() {
				const script = document.createElement('script');
				script.src = 'https://cstaticdun.126.net/load.min.js?t=201903281201';
				document.head.appendChild(script);
			}
		};

		methods.init();

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});

import { defineComponent, reactive, toRefs } from 'vue';
import Utils from '@/utils/utils';
import { JxSimpleInfoResponse, StateModel } from './types';
import Deferred, { DeferredModel } from '@/utils/deferred';
import { CoachItemModel, JiaxiaoItemModel, RecommendModel } from '@/application/index/page/comp/form/types';
import { PostSimpleInfoStore } from '@/store/awd/h5';

export default defineComponent({
	setup() {
		let dtd: DeferredModel<void> = null;

		const state = reactive(<StateModel>{
			visible: false, // 是否显示弹框
			name: '',
			privatePhone: ''
		});

		const methods = {
			// 显示弹框
			showDialog() {
				// 只有android可以拨打电话
				if (state.privatePhone && Utils.isAndroid) {
					state.visible = true;
				} else {
					return Promise.resolve();
				}

				dtd = Deferred();

				return dtd.promise;
			},
			// 关闭弹框
			close() {
				state.visible = false;

				methods.reset();
			},
			reset() {
				dtd = null;
			},
			// 暂不拨打
			onCancel() {
				dtd.resolve();
				methods.close();
			},
			// 立即拨打
			onSubmit() {
				Utils.callPhone('', {
					[state.name]: state.privatePhone
				});
				setTimeout(() => {
					dtd.resolve();
				}, 800);
			},
			// 接收父组件的数据
			setCallInfo(recommend: RecommendModel, list: JiaxiaoItemModel[] | CoachItemModel[]) {
				if (recommend.listSource === 'jiaxiao') {
					// 获取是否有隐私号码，提前调用，增加用户体验
					PostSimpleInfoStore({
						jiaxiaoId: list[0].id
					}).then((res: JxSimpleInfoResponse) => {
						if (res) {
							state.name = res.name;
							state.privatePhone = res.privatePhone;
						}
					});
				}
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});

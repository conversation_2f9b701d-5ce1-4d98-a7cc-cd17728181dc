import { defineComponent, reactive, toRefs } from 'vue';
import Deferred, { DeferredModel } from '@/utils/deferred';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';
import { PullSuccessEnum } from '@/utils/hooks/abtest/constants';
import { trackEvent } from '@/utils/stat';
import {
	DEFAULT_CANCEL,
	DEFAULT_CONTENT,
	DEFAULT_SUBMIT
} from '@/application/index/page/comp/dialog-double-confirm/constants';

export default defineComponent({
	setup() {
		let dtd: DeferredModel<void> = null;

		const remoteSettingStore = useRemoteSettingStore();

		const state = reactive({
			visible: false
		});

		const constants = {
			DEFAULT_CONTENT,
			DEFAULT_SUBMIT,
			DEFAULT_CANCEL
		};

		const components = {};

		const methods = {
			async show() {
				await methods.getContent();

				trackEvent({
					PageName: '帮我找驾校页h5',
					actionName: '二次确认弹窗-展示',
					actionType: '',
					numbers: {
						content: DEFAULT_CONTENT
					}
				});

				const visible = remoteSettingStore.state.reconfirm;

				if (!visible) {
					return Promise.resolve();
				}

				state.visible = true;

				dtd = Deferred();

				return dtd.promise;
			},
			onCancel() {
				trackEvent({
					PageName: '帮我找驾校页h5',
					actionName: '二次确认弹窗-点击取消',
					actionType: '',
					numbers: {
						content: DEFAULT_CANCEL
					}
				});

				dtd.resolve();

				methods.close();
			},
			onSubmit() {
				trackEvent({
					PageName: '帮我找驾校页h5',
					actionName: '二次确认弹窗-点击确认',
					actionType: '',
					numbers: {
						content: DEFAULT_SUBMIT
					}
				});

				dtd.resolve();

				methods.close();
			},
			close() {
				state.visible = false;

				methods.reset();
			},
			reset() {
				dtd = null;
			},
			getContent(): Promise<PullSuccessEnum> {
				return new Promise(resolve => {
					remoteSettingStore.getReconfirmDialog().then(bool => {
						trackEvent({
							PageName: '帮我找驾校页h5',
							actionName: '二次确认弹窗',
							actionType: '',
							numbers: {
								check: [PullSuccessEnum.ERROR, PullSuccessEnum.SUCCESS].includes
									? bool
									: PullSuccessEnum.DEFAULT
							}
						});
						resolve(bool);
					});
				});
			}
		};

		return {
			remoteSettingStore,
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});

<template>
	<div class="dialog-mask" v-if="visible">
		<div class="dialog-content">
			<p>{{ remoteSettingStore.state.reconfirmInfo?.content || DEFAULT_CONTENT }}</p>
			<div class="btn-list">
				<div class="btn btn-left" @click="onCancel">
					{{ remoteSettingStore.state.reconfirmInfo?.cancel || DEFAULT_CANCEL }}
				</div>
				<div class="btn btn-right" @click="onSubmit">
					{{ remoteSettingStore.state.reconfirmInfo?.submit || DEFAULT_SUBMIT }}
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>

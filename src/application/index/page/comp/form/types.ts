import { SourceEnum, TargetTypeEnum } from '@/application/index/page/comp/form/constants';

export interface StateModel {
	phone: string; // 手机号码
	name: string; // 您的称呼
	isNameClick: boolean; // 是否触发过姓名input
	isPhoneClick: boolean; // 是否触发过手机号input
	address: AddressModel; // 学车地址
	carType: string; // 驾照类型
	carDesc: string;
	protocolChoose: boolean; // 隐私政策
	isLogin: boolean; // 是否一键登录/验证码登录
}

export interface OortModel {
	banLogin: number; // 一键登录标识
	isNameClick: boolean; // 是否触发过姓名input
	isPhoneClick: boolean; // 是否触发过手机号input
}

// 学车地址
export interface AddressModel {
	cityCode: string;
	name: string;
	location: {
		lng: number;
		lat: number;
		adcode: string;
		province: string;
		city: string;
		district: string;
	};
}

export interface RecommendModel {
	// 推荐驾校列表数据来源
	listSource: SourceEnum;
	/** 推荐驾校列表数据 */
	jiaxiaoList: JiaxiaoItemModel[];
	/** 推荐教练列表的数据 */
	coachList: CoachItemModel[];
}

export interface JiaxiaoPhoneResponse {
	/**  */
	privatePhone: string;
	/**  */
	complaintPhone: string;
	/**  */
	phoneStatus: number;
}

export interface ActivityResponse {
	/**  */
	name: string;
	/**  */
	amount: string;
	/**  */
	id: number;
	/**  */
	url: string;
	/**  */
	type: string;
	/** 可用值:LIMIT_TIME,GROUP_BUYING,LOTTERY_ACTIVITY,RED_PACKET */
	activityEnum: string;
	/**  */
	price: number;
	/**  */
	subTitle: string;
	/**  */
	received: boolean;
	/**  */
	receivedCount: number;
	/**  */
	endSeconds: number;
	/**  */
	backgroundPic: string;
	/**  */
	buttonPic: string;
	/**  */
	titlePic: string;
	/**  */
	contentColor: string;
	/**  */
	countDownColor: string;
	/**  */
	driveLicenseType: string;
}

export interface JiaxiaoItemModel {
	/** 驾校Id */
	id: number;
	/** 驾校name */
	name: string;
	/** 驾校区县 */
	countyName: string;
	/** 驾校评分 */
	score: any;
	/** 驾校评价条数 */
	dianpingCount: number;
	/** 驾校距离 */
	distance: any;
	/** 驾校距离 */
	distanceDesc: string;
	/** 驾校班型 */
	course: string;
	/** 驾校标签列表 */
	labels: string[];
	/** 驾校logo图标的地址 */
	logoIcon: string;
	/** 驾校特权图标URL */
	privileges: string[];
	/** 驾校logo */
	logo: string;
	/** 驾校排名信息 */
	rank: string;
	/** 是否有vr */
	hasVr: boolean;
	/** 是否有视频 */
	hasVideo: boolean;
	/**  */
	jiaxiaoPhone: JiaxiaoPhoneResponse;
	/** 驾校地址 */
	address: string;
	/** 驾校邮箱 */
	email: string;
	/** 班型名称 */
	courseName: string;
	/** 班型价格 */
	coursePrice: string;
	/** 驾校活动 */
	activity: ActivityResponse[];
	/**  */
	vipYear: number;
	/**  */
	vipLevel: number;
}

export interface CoachItemModel {
	/** 教练ID */
	id: number;
	/** 教练姓名 */
	name: string;
	/** 教练头像 */
	avatar: string;
	/** 教练头像框 */
	avatarFrame: string;
	/** 驾校ID */
	jiaxiaoId: number;
	/** 驾校名称 */
	jiaxiaoName: string;
	/** 教练标签 */
	labels: string[];
	/** 距离 */
	distance: string;
	/** 距离文案 */
	distanceText: string;
	/** VIP图标 */
	vipIcon: string;
	/** 路考仪图标 */
	lukaoyiIcon: string;
	/** 优选教练图标 */
	priorCoachIcon: string;
	/** 杰出教练图标 */
	goldCoachIcon: string;
	/** 班型价格 */
	price: string;
	/** 教练评分 */
	score: any;
	/** 教练排名分 */
	rankScore: any;
	/** 教练排名描述 */
	rankDesc: string;
	/** 教练排名名次 */
	rankNum: number;
	/** 教练活动 */
	activity: ActivityResponse[];
	/** 教龄 */
	teachAge: number;
	/** 教练区县 */
	countyName: string;
	/** 点评数量 */
	dianpingCount: number;
	/** 班型名称 */
	courseName: string;
	/** 班型价格 */
	coursePrice: string;
}

export interface UrlQueryModel {
	// 默认驾照类型
	carStyle?: string;
	// 默认jiaxiaoId
	jiaxiaoId?: string;
	// 是否跳转问卷页面，1跳转
	information?: string;
	// 进入入口
	entrance?: 'newuser' | 'homepage';
	// 安卓预加载标识，url上面有这个参数就代表当前是预加载页面，不需要处理逻辑
	forbidReportLog?: string;
	// 区分留资前置/后置，1后置
	postFlow?: string;
}

export interface BaseParamsModel {
	// 根据当前时间生成的订单号
	orderNo: string;
	// 您的称呼
	userName: string;
	// 手机号码
	userPhone: string;
	// 经度
	userLongitude: number;
	// 纬度
	userLatitude: number;
	// 驾照类型
	userLicenseType: string;
	// mucangId
	userId: string;
	// 学车地址
	userAddress: string;
	// 推荐弹框类型
	targets: TargetModel[] | string;
	// 线索提交入口
	submitPoint: number;
	// 入口页面1
	entrancePage1: string;
	// 入口页面2
	entrancePage2: string;
	// 是否已登录
	tokenLead: boolean;
	// 定位信息
	accuracy: number;
	// 学车地址cityCode
	userAreaCode: string;
	// 推荐弹框类型
	listSource: string;
	// 提交留资耗费时长
	meta: string;
	// 留资时间
	clientCreateTime: number;
	// 无人机的留资信息
	userLicenseTypeExt?: string;
	userLicenseTypeExt2?: string;
}

export interface TargetModel {
	// 询价的教练/驾校id
	targetId: number;
	// 0非定向，2教练，3驾校
	targetType: TargetTypeEnum;
}

export interface SimpleAreaResponse {
	fullName: string;
	simpleCode: string;
	simpleName: string;
}

export interface EmitModel {
	(e: 'submitEnd', item: BaseParamsModel);
}

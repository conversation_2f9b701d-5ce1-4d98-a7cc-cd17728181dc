import { trackEvent } from '@/utils/stat';
import { ToastEnum } from '@/application/index/page/comp/form/constants';
import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';

// 表单校验的埋点
export const oortToast = (type: ToastEnum) => {
	trackEvent({
		PageName: '帮我找驾校页h5',
		actionName: 'toast显示类型',
		actionType: '',
		numbers: {
			toastType: type
		}
	});
};

// 留资错误上报
export const saveErrorReq = (url: string, params): Promise<void> => {
	return new Promise(resolve => {
		// @ts-ignore
		MCProtocol['jiakao-global'].saveErrorReq({
			reqConfig: JSON.stringify({
				host: APP.domain.lead,
				signKey: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9',
				path: url,
				// ios需要手动添加参数进行加密，安卓默认已进行加密
				params: Utils.isIOS ? JSON.stringify({
					...params,
					needEncrypted: true
				}) : JSON.stringify(params),
				expireTime: new Date().getTime() + 180 * 24 * 60 * 60 * 1000
			}),
			callback: function (data) {
				console.log('saveErrorReq callback: ', data);
				resolve();
			}
		});
	});
};

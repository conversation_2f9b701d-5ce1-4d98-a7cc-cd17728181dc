// 是否可一键登录枚举
export const quickLoginEnum = {
	DEFAULT: -1, // 默认值
	NOT_LOGIN: 0, // 不能一键登录
	CAN_LOGIN: 1 // 可以一键登录
};

// 推荐驾校列表数据来源
export enum SourceEnum {
	JIAXIAO = 'jiaxiao',
	COACH = 'coach',
	// 下面三个已经废弃，接口可能返回此类型，但是不再使用
	RANK_COACH = 'rankCoach',
	RANK_JIAXIAO = 'rankJiaxiao',
	PHONE = 'phone'
}

// 线索类型 0非定向，2教练，3驾校
export enum TargetTypeEnum {
	NONE = 0,
	COACH = 2,
	JIAXIAO = 3
}

// 0称呼，1手机号，2学车地址，3隐私政策，4先发送验证码，5填写验证码
export enum ToastEnum {
	NAME = 0,
	PHONE = 1,
	ADDRESS = 2,
	PROTOCOL = 3,
	SEND_SMS = 4,
	FILL_CODE = 5
}
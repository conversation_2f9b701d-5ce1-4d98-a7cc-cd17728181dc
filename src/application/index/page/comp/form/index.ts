import MCProtocol from '@simplex/simple-mcprotocol';
import { computed, defineComponent, PropType, reactive, ref, toRefs } from 'vue';
import Utils from '@/utils/utils';
import UI from '@/utils/ui';
import { trackEvent } from '@/utils/stat';
import useRouter from '@/utils/hooks/router';
import { REG_RULE } from '@/utils/constant';
import { ADDRESS, CAR_DESC, CAR_TYPE, DURATION, HANDLE_CHOOSE_LAT_BOOL, NEW_USER_LEAD_BOOL } from '@/utils/storage-key';
import DialogLicenseComp from '@/application/index/page/comp/dialog-license/index.vue';
import { GetSimpleAreaStore } from '@/store/arare/h5';
import { PostAdd2Store, PostAddPartStore, PostAddUavStore } from '@/store/lead/h5';
import {
	BaseParamsModel,
	EmitModel,
	SimpleAreaResponse,
	StateModel,
	UrlQueryModel
} from '@/application/index/page/comp/form/types';
import { QuickLoginResponse } from '@/application/index/page/types';
import { oortToast, saveErrorReq } from '@/application/index/page/comp/form/utils';
import { LoginResponse } from '@/application/index/page/comp/verify-phone-code/types';
import VerifyPhoneComp from '@/application/index/page/comp/verify-phone/index.vue';
import { ItemModel } from '@/application/index/page/comp/dialog-license/types';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';
import { ToastEnum } from '@/application/index/page/comp/form/constants';
import { UAV_LICENSE_LIST } from '@/utils/options/license';

let isGoLicense = false; // 是否已进入过驾照类型页面
let authToken = '';
let mucangId = '';

export default defineComponent({
	components: {
		VerifyPhoneComp,
		DialogLicenseComp
	},
	props: {
		userCity: {
			type: String,
			required: true
		},
		urlQuery: {
			type: Object as PropType<UrlQueryModel>,
			default: (): UrlQueryModel => ({})
		},
		isBanLogin: {
			type: Boolean,
			required: true
		}
	},
	setup(props, { emit }: { emit: EmitModel }) {
		const router = useRouter();

		const remoteSettingStore = useRemoteSettingStore();

		const state = reactive(<StateModel>{
			// 手机号码
			phone: '',
			// 您的称呼,
			name: '',
			// 是否触发过姓名input
			isNameClick: false,
			// 是否触发过手机号input
			isPhoneClick: false,
			// 学车地址
			address: {},
			// 驾照类型
			carType: '',
			carDesc: '',
			// 隐私政策
			protocolChoose: false,
			// 是否一键登录/验证码登录
			isLogin: false
		});

		const computeds = {
			carFull: computed(() => state.carType + ' ' + state.carDesc)
		};

		const components = {
			verifyPhoneRef: ref<InstanceType<typeof VerifyPhoneComp>>(),
			dialogLicenseRef: ref<InstanceType<typeof DialogLicenseComp>>()
		};

		const methods = {
			// 回填storage参数
			getStorage() {
				// Utils.store.clear();
				const carType = Utils.store.get(CAR_TYPE);
				const carDesc = Utils.store.get(CAR_DESC);
				state.carType = carType || state.carType;
				state.carDesc = carDesc || state.carDesc;

				const address = Utils.store.get(ADDRESS);
				state.address = address ? JSON.parse(address) : state.address;
			},
			// 清空手机号，恢复未登录状态
			clearPhone() {
				state.phone = '';
				state.isLogin = false;
			},
			// 跳转到学车地址，存储页面信息
			goMap() {
				router.goPathJumps({
					path: 'map.html',
					coreOpenParams: {
						titleBar: false,
						orientation: 'portrait',
						button: '',
						menu: false
					}
				});
			},
			// 跳转到驾照类型，存储页面信息
			goLicense() {
				isGoLicense = true;
				Utils.store.set(CAR_TYPE, state.carType);
				router.goPathJumps({
					path: 'license.html',
					coreOpenParams: {
						titleBar: false,
						orientation: 'portrait',
						button: '',
						menu: false
					}
				});
			},
			// 跳转到隐私政策
			goPrivate() {
				MCProtocol.Core.Web.open({
					titleBar: true,
					title: '隐私政策',
					orientation: 'portrait',
					button: '',
					menu: false,
					url: 'https://laofuzi.kakamobi.com/agreements/privateAgreement.html'
				});
			},
			// 跳转到个人信息保护声明
			goProtect() {
				const cityCode = state.address.cityCode || props.userCity;
				MCProtocol.Core.Web.open({
					titleBar: true,
					title: '个人信息保护声明',
					orientation: 'portrait',
					button: '',
					menu: false,
					url: `https://share-m.kakamobi.com/activity.kakamobi.com/jiaxiaozhijia-information-protection/?addressCode=${cityCode}`
				});
			},
			// 表单校验
			checkForm() {
				if (!REG_RULE.phone.test(state.phone) && !state.isLogin) {
					UI.toast('请填写正确的手机号');
					oortToast(ToastEnum.PHONE);
					return false;
				}
				if (!REG_RULE.name.test(state.name)) {
					UI.toast('请输入中文姓名');
					oortToast(ToastEnum.NAME);
					return false;
				}
				if (!state.address.cityCode) {
					UI.toast('请选择学车地址');
					oortToast(ToastEnum.ADDRESS);
					return false;
				}
				if (!state.carType) {
					UI.toast('请选择驾照类型');
					return false;
				}
				if (!state.protocolChoose) {
					UI.toast('您需要同意隐私政策和个人信息保护声明才能提交');
					oortToast(ToastEnum.PROTOCOL);
					return false;
				}

				return true;
			},
			// 提交
			onSubmit: Utils.debounce(async () => {
				trackEvent({
					PageName: '帮我找驾校页h5',
					actionName: '点击“提交学车意向”按钮',
					actionType: '',
					numbers: {
                        // 1修改过称呼并且不为空，0默认
						nameInput: state.isNameClick && state.name !== '' ? 1 : 0
					}
				});

				// 表单校验
				if (!methods.checkForm()) {
					return;
				}

				// 如果没登录，弹出验证码登录
				if (!state.isLogin) {
					await methods.onSubmitAuth();
				}

				// 登录成功，打开摩托车驾照配置选择弹窗
				await methods.showMotoDialog();

				// 获取请求的基础参数
				const baseParams = await methods.getParams();

				// 留资
				methods.postChev(baseParams);

				// 留资结束
				emit('submitEnd', baseParams);
			}),
			// 阿里云校验当前手机号成功直接登录，否则走验证码逻辑，需要打开数据流量
			onSubmitAuth(): Promise<void> {
				return new Promise((resolve) => {
					MCProtocol.Core.User.numAuth({
						phone: state.phone,
						callback: async res => {
							if (res.data === 'PASS') {
								resolve();
							} else {
								const codeData: LoginResponse = await components.verifyPhoneRef.value.open(state.phone);

								authToken = codeData.authToken;
								mucangId = codeData.mucangId;
								state.isLogin = true;

								resolve();
							}
						}
					});
				});
			},
			// 摩托车弹窗
			showMotoDialog(): Promise<void> {
				return new Promise((resolve, reject) => {
					remoteSettingStore.getMotoDialog(state.address.cityCode).then(bool => {
						if (!isGoLicense && state.carType === 'C1' && bool) {
							components.dialogLicenseRef.value
								.show()
								.then((item: ItemModel) => {
									state.carType = item.carType;
									state.carDesc = item.carDesc;
									resolve();
								})
								.catch(() => {
									reject();
								});
						} else {
							resolve();
						}
					});
				});
			},
			// 封装留资方法
			postChev(params: BaseParamsModel): Promise<void> {
				return new Promise((resolve, reject) => {
					/*
						1. locationByNewUserLead 已拉取
						2. 沒有手动选择地址
					*/
					// locationByNewUserLead 已拉取 代表当前是模糊定位
					// 同时没有手动选取精确地址
					// 所以使用分步留资的接口保持留资数据，让客户端二次上报
					const getNewUserLeadBool = Utils.store.get(NEW_USER_LEAD_BOOL);
					const getHandleChooseLatBool = Utils.store.get(HANDLE_CHOOSE_LAT_BOOL);
					let bool = getNewUserLeadBool && !getHandleChooseLatBool;

					const postMethod = (() => {
						if (UAV_LICENSE_LIST.includes(params.userLicenseType)) {
							// 无人机类型增加字段
							const [droneType, licenseType] = params.userLicenseType.split('__');
							params.userLicenseType = droneType;
							params.userLicenseTypeExt = 'S';
							params.userLicenseTypeExt2 = licenseType;

							// 无人机没有分步提交
							bool = false;

							return PostAddUavStore;
						}

						return bool ? PostAddPartStore : PostAdd2Store;
					})();

					let postData: BaseParamsModel | { content: string; finished: boolean; type: number } = {};
					if (!bool) {
						postData = params;
						params.targets = JSON.stringify(params.targets);
						// 重新生成订单号
						params.orderNo = Utils.getOrderNo();
					} else {
						// 分步提交提前通知客户端-orderNo
						Utils.isAndroid &&
							Utils.partSendOrderNo({
								orderNo: params.orderNo
							});
						postData = {
							content: JSON.stringify(params),
							finished: false,
							type: 1
						};
					}

					UI.loading();
					console.log('留资参数：');
					console.log(postData);

					postMethod(postData)
						.then((res: { result: boolean }) => {
							if (res.result) {
								MCProtocol.jxxy.enquiry.finished();
							} else {
								UI.toast('网络异常');
							}
							resolve();
						})
						.catch(err => {
							Utils.isAndroid &&
								Utils.partSendOrderNo({
									orderNo: ''
								});
							saveErrorReq('', postData);
							UI.toast(err.message);
							reject();
						})
						.finally(() => {
							UI.loadingHide();
						});
				});
			},
			inputPhone() {
				state.isNameClick = true;
			},
			inputName() {
				state.isPhoneClick = true;
			},
			// 一键登录回填用户信息
			setUserInfo(res: QuickLoginResponse) {
				if (res.success) {
					state.phone = res.data.phone;
					state.name = res.data.nickname;
					authToken = res.data.authToken;
					mucangId = res.data.mucangId;
					state.isLogin = true;
				} else {
					state.isLogin = false;
				}
			},
			// 获取基础参数
			getParams(): Promise<BaseParamsModel> {
				return new Promise(resolve => {
					Promise.all([
						Utils.postAccuracyKey(),
						// 防止cityCode不一致
						GetSimpleAreaStore({
							adcode: state.address.cityCode,
							province: state.address.location.province,
							city: state.address.location.city,
							district: state.address.location.district
						})
					]).then(([accuracyKey, simpleArea]) => {
						const { accuracy } = accuracyKey;
						const { simpleCode } = simpleArea as SimpleAreaResponse;

						state.address.cityCode = simpleCode || state.address.cityCode;
						const durationEnd = parseInt(Utils.store.get(DURATION));
						const meta = {
							Duration: new Date().getTime() - durationEnd
						};
						const baseParams: BaseParamsModel = {
							orderNo: Utils.getOrderNo(),
							userName: state.name || '驾考用户',
							userPhone: state.phone,
							userLongitude: state.address.location.lng,
							userLatitude: state.address.location.lat,
							userLicenseType: state.carType,
							userId: mucangId,
							userAddress: state.address.name,
							targets: [
								{
									// *询价的教练or驾校id， 如果targetType=0，则targetId也为0
									targetId: props.urlQuery.jiaxiaoId ? Number(props.urlQuery.jiaxiaoId) : 0,
									//*0=非定向 2=教练 3=驾校
									targetType: props.urlQuery.jiaxiaoId ? 3 : 0
								}
							],
							submitPoint: 0,
							entrancePage1: Utils.setEntrancePage({
								isBanLogin: props.isBanLogin
							}),
							entrancePage2:
								props.urlQuery.entrance === 'newuser' ? 'new-user-process-66' : 'homepage-entrance-66',
							tokenLead: state.isLogin,
							accuracy: accuracy,
							userAreaCode: state.address.cityCode,
							listSource: 'default',
							meta: JSON.stringify(meta),
							clientCreateTime: new Date().getTime()
						};

						resolve(baseParams);
					});
				});
			}
		};

		return {
			...toRefs(state),
			...computeds,
			...components,
			...methods
		};
	}
});

import { computed, defineComponent, reactive, toRefs } from 'vue';
import { RecommendReturn, StateModel } from './types';
import { CoachItemModel, JiaxiaoItemModel, TargetModel } from '@/application/index/page/comp/form/types';
import { SourceEnum, TargetTypeEnum } from '@/application/index/page/comp/form/constants';
import Deferred, { DeferredModel } from '@/utils/deferred';

export default defineComponent({
	setup(prop, { emit }) {
		let dtd: DeferredModel<RecommendReturn> = null;

		const state = reactive<StateModel>({
			// 是否显示弹框
			visible: false,
			// 推荐数据
			recommendData: null,
			// 推荐类型
			recommendType: null,
			// 评分样式宽度
			scoreWidth: ''
		});

		const computeds = {
			isJiaxiao: computed(() => state.recommendType === SourceEnum.JIAXIAO)
		};

		const methods = {
			// 显示弹框
			show(recommendData: JiaxiaoItemModel | CoachItemModel, type: SourceEnum) {
				if (recommendData) {
					state.recommendType = type;

					state.scoreWidth = ((recommendData.score || 3) / 5) * 100 + '%';
					if (type === SourceEnum.JIAXIAO) {
						const jiaxiaoData = recommendData as JiaxiaoItemModel;

						jiaxiaoData.privileges = jiaxiaoData.vipLevel
							? jiaxiaoData.privileges.splice(0, 1)
							: jiaxiaoData.privileges.splice(0, 2);

						recommendData = jiaxiaoData;
					}
					recommendData.labels =
						recommendData.labels.length > 3 ? recommendData.labels.splice(0, 3) : recommendData.labels;
					state.recommendData = recommendData;

					state.visible = true;
				}

				dtd = Deferred();

				return dtd.promise;
			},
			// 关闭弹框
			close() {
				state.visible = false;

				methods.reset();
			},
			reset() {
				dtd = null;
			},
			// 1v1 咨询
			submitOnly() {
				// 驾校：3，教练：2
				const targetType = computeds.isJiaxiao.value ? TargetTypeEnum.JIAXIAO : TargetTypeEnum.COACH;
				const targets: TargetModel[] = [{ targetId: state.recommendData.id, targetType }];

				dtd.resolve({
					interested: true,
					targets
				});
				methods.close();
			},
			// 1vn 不感兴趣
			submitAll() {
				dtd.resolve({
					interested: false,
					targets: []
				});
				methods.close();
			}
		};
		return {
			...toRefs(state),
			...computeds,
			...methods
		};
	}
});

@import url(@/assets/style/dialog.less);

.dialog-mask {
	.dialog-content {
		h3 {
			font-size: 18px;
			font-weight: 600;
			text-align: center;
			color: #333333;
			margin-top: 9px;
			margin-bottom: 18px;
		}
		.content-box {
			width: 100%;
			display: flex;
			.logo {
				position: relative;
				min-width: 84px;
				width: 84px;
				height: 70px;
				border-radius: 4px;
				margin-right: 10px;
				i {
					position: absolute;
					top: -3px;
					left: 0;
					width: 33px;
					height: 19px;
				}
			}
			.right {
				flex: 1;
				.jiaxiao {
					display: flex;
					align-items: center;
					margin-bottom: 5px;
					span {
						font-size: 18px;
						font-weight: 600;
						max-width: 130px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						line-height: 20px;
					}
					.jiaxiao_name {
						font-size: 12px;
						text-align: left;
						color: #6e6e6e;
						line-height: 17px;
						margin-left: 8px;
					}
					i {
						display: block;
						width: 20px;
						height: 20px;
					}
					.icon_0 {
						background: url('./img/lavel_one.png') no-repeat center;
						background-size: 100% auto;
						text-align: center;
						color: #81b6ff;
					}
					.icon_30 {
						background: url('./img/lavel_two.png') no-repeat center;
						background-size: 100% auto;
						color: #ffb633;
					}
					.icon_80 {
						background: url('./img/lavel_three.png') no-repeat center;
						background-size: 100% auto;
						color: #a89eff;
					}
					.icon_100 {
						background: url('./img/lavel_four.png') no-repeat center;
						background-size: 100% auto;
						color: #c37706;
					}
					.vip {
						position: relative;
						width: 50px;
						height: 20px;
						span {
							position: absolute;
							left: 50%;
							top: 3px;
							font-size: 10px;
							font-weight: normal;
						}
					}
				}
				.course {
					display: flex;
					align-items: center;
					margin-bottom: 10px;
					.icon {
						position: relative;
						width: 90px;
						height: 15px;
						margin-right: 10px;
						.hui {
							position: relative;
							width: 90px;
							height: 15px;
							background: url('./img/star-hui.png') no-repeat;
							background-size: cover;
							z-index: 1;
						}
						i {
							display: block;
							position: absolute;
							z-index: 2;
							top: 0;
							left: 0;
							height: 15px;
							background: url('./img/star-yellow.png') no-repeat;
							background-size: cover;
						}
					}
					span {
						display: block;
						padding-top: 1px;
						height: 15px;
						line-height: 15px;
						font-size: 12px;
						color: #ff822d;
					}
					.dianping_num {
						margin-left: 10px;
						font-size: 12px;
						font-weight: normal;
						text-align: left;
						color: #6e6e6e;
						line-height: 12px;
					}
				}
				.rank_item {
					display: flex;
					align-items: center;
					.rank {
						width: fit-content;
						padding: 1px 2px;
						background: rgba(255, 130, 45, 0.12);
						border-radius: 4px;
						font-size: 12px;
						color: #7b510b;
						margin-right: 5px;
					}
					.dianping {
						height: 18px;
						font-size: 12px;
						text-align: left;
						color: #6e6e6e;
						line-height: 18px;
						margin-left: 8px;
					}
				}
				.banke {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 12px;
					font-weight: normal;
					text-align: left;
					color: #6e6e6e;
					line-height: 17px;
					.banke_left_mr10 {
						margin-right: 8px;
					}
				}
				.dianping {
					height: 18px;
					font-size: 13px;
					text-align: left;
					color: #6e6e6e;
					line-height: 18px;
				}
			}
		}
		.distance {
			margin-top: 20px;
			// height: 28px;
			line-height: 28px;
			padding: 0px 10px;
			box-sizing: content-box;
			background: #f5fafc;
			border-radius: 8px;
			font-size: 13px;
			color: #333333;
		}
	}
}

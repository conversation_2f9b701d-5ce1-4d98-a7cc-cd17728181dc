// ui数据模型
export interface StateModel {
	show: boolean;
	shouzimuList: Array<[]>;
	shouzimuMaps: object;
}
export interface EmitModel {
	(e: 'updateCityCode', cityData: CityItemModel): void;
}
// 接口数据模型
export interface StoreResponse {
	content: string; // 内容
	createTime: number; // 创建时间
	id: number; // 序号
	module: number; // 所属模块code
	moduleName: string; // 所属模块名
	read: boolean; // 是否已读
	title: string; // 标题
	userId: string; // 用户id
}
export interface CityTypeModel {
	code: string;
	name: string;
	pinyin: string;
	cities?: Array<CityItemModel>;
	cityOrZhixiashi?: boolean;
}
interface CityItemModel {
	code: string;
	name: string;
	pinyin: string;
}

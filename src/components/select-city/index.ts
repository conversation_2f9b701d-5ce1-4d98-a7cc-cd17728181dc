import { defineComponent, ref, reactive, toRefs } from 'vue';
import { StateModel, EmitModel } from './types';
import { ALL_CITY, HOT_CITY, NAVZM } from './constants';

export default defineComponent({
	name: 'select-city',
	setup(prop, { emit }: { emit: EmitModel }) {
		// 内部属性
		const state = reactive<StateModel>({
			// 初始化显示
			show: false,
			// 城市字母导航
			shouzimuList: [],
			// 首字母
			shouzimuMaps: {}
		});

		// 常量
		const constants = {
			// 热门城市
			hotCity: HOT_CITY
		};

		// 页面组件实例集合
		const components = {
			divEl: ref<HTMLDivElement>(null)
		};
		// 方法
		const methods = {
			onShow() {
				state.show = true;
			},
			onHide() {
				state.show = false;
			},
			// 初始化处理数据
			initData() {
				const shouzimuList = [];
				const shouzimuMaps = {};
				const shouzimu = {};

				for (let j = 0; j < 26; j++) {
					shouzimu[NAVZM.charAt(j).toUpperCase()] = [];
				}
				for (let i = 0; i < ALL_CITY.length; i++) {
					const pros = ALL_CITY[i];
					let cities = pros.cities;

					if (pros.cityOrZhixiashi) {
						cities = [pros];
					}
					for (let u = 0; u < cities.length; u++) {
						const zimu = cities[u].pinyin.charAt(0).toUpperCase();
						shouzimu[zimu] = 1;
						if (!shouzimuMaps[zimu]) {
							shouzimuMaps[zimu] = [cities[u]];
						} else {
							shouzimuMaps[zimu].push(cities[u]);
						}
					}
				}

				for (const key in shouzimu) {
					if (shouzimu[key] === 1) {
						shouzimuList.push(key);
					}
				}
				state.shouzimuList = shouzimuList;
				state.shouzimuMaps = shouzimuMaps;
			},
			// 导航点击
			gotoLetter(letter) {
				const $dom = document.getElementById(letter);
				const $nodes: HTMLElement = <HTMLElement>document.getElementsByClassName('select-city-box')[0];
				if ($nodes) {
					$nodes.scrollTop = $dom.offsetTop;
				}
			},
			// 转id
			ki(m) {
				return m;
			},
			// 城市选择
			selectCity(cityData) {
				emit('updateCityCode', cityData);
				methods.onHide();
			}
		};

		methods.initData();

		return {
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});

.com-select {
	.select-city {
		position: fixed;
		top: 30px;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		overflow-y: auto;
		z-index: 999;
		transition: top 0.3s;
		// 兼容phone-x之后的机型
		padding-top: constant(safe-area-inset-top); //为导航栏+状态栏的高度 88px
		padding-top: env(safe-area-inset-top); //为导航栏+状态栏的高度 88px
		.icon {
			width: 40px;
			height: 40px;
			background-image: url('https://web-resource.mucang.cn/web/demo-jiaxiao/sketch31424391-0e6a-426c-87a5-a4ffd1ee6e53.png');
			background: url('https://web-resource.mucang.cn/web/demo-jiaxiao/sketch31424391-0e6a-426c-87a5-a4ffd1ee6e53.png')
				no-repeat left;
			background-size: 15px 15px;
		}
		.hot-wrap {
			padding: 15px;
			.hot {
				font-size: 14px;
				color: #333;
				line-height: 14px;
				padding-bottom: 5px;
			}
			.hot-r {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				.hot-item {
					width: 33%;
					margin-top: 10px;
					font-family: PingFangSC-Regular;
					font-size: 14px;
					color: #333;
					height: 30px;
					background: #f7f7fa;
					border-radius: 4px;
					line-height: 30px;
					text-align: center;
					display: block;
				}
			}
		}
		.zimu-wrap {
			position: relative;
			.zimu-group {
				height: 26px;
				background: #efeff4;
				font-size: 14px;
				color: #888;
				line-height: 26px;
				padding-left: 15px;
			}
			.zimu-queen {
				padding-left: 15px;
				height: 44px;
				span {
					display: block;
					height: 44px;
					font-size: 17px;
					color: #333;
					line-height: 44px;
					border-bottom: 1px solid #e5e5e5;
				}
			}
		}
		.zimu-nav {
			position: fixed;
			right: 0;
			top: 50%;
			width: 22px;
			font-family: PingFangSC-Regular;
			font-size: 16px;
			color: #333;
			line-height: 18px;
			text-align: center;
			transform: translate(0, -50%);
		}
	}
}

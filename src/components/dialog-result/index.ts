import { defineComponent, reactive, toRefs } from 'vue';
import { trackEvent } from '@/utils/stat';
import { StateModel } from './types';
import { TypeEnum } from '@/components/dialog-result/constants';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';
import Deferred, { DeferredModel } from '@/utils/deferred';

export default defineComponent({
	setup() {
		let dtd: DeferredModel<void> = null;

		const remoteSettingStore = useRemoteSettingStore();

		const state = reactive<StateModel>({
			// 是否显示弹框
			visible: false,
			// 1留资成功，2取消留资
			type: null
		});

		const methods = {
			async show(type: TypeEnum, cityCode: string) {
				state.type = type;
				const code = cityCode.slice(0, 4) + '00';

				const visible = await remoteSettingStore.getNewUserDialog(code, type);

				if (!visible) {
					return Promise.resolve();
				}

				state.visible = true;
				trackEvent({
					PageName: '帮我找驾校页h5-66弹框',
					actionName: '展示',
					actionType: ''
				});

				dtd = Deferred();

				return dtd.promise;
			},
			// 跳转
			jumpTo66() {
				methods.close();

				const searchData =
					location.search && location.search.indexOf('?') > -1 ? location.search : `?${location.search}`;
				let refNewStr = '_ref=jiaxiao1#/';

				if (state.type === TypeEnum.SUBMIT) {
					// 留咨成功的
					refNewStr = '_ref=jiaxiao1#/';
				} else if (state.type === TypeEnum.DETERRENT) {
					// 是挽留的
					refNewStr = '_ref=jiaxiao2#/';
				}

				location.replace(remoteSettingStore.state.newUserUrl + searchData + '&' + refNewStr);

				trackEvent({
					PageName: '帮我找驾校页h5-66弹框',
					actionName: '点击跳转',
					actionType: '',
					sourceRef: state.type === TypeEnum.SUBMIT ? 'jiaxiao1' : 'jiaxiao2' // jiaxiao1留资成功，jiaxiao2取消留资
				});
			},
			onClose() {
				dtd.resolve();

				methods.close();
			},
			// 关闭弹框
			close() {
				state.visible = false;
				methods.reset();

				trackEvent({
					PageName: '帮我找驾校页h5-66弹框',
					actionName: '点击关闭',
					actionType: ''
				});
			},
			reset() {
				dtd = null;
			}
		};

		return {
			remoteSettingStore,
			...toRefs(state),
			...methods
		};
	}
});

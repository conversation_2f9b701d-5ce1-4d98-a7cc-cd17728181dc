import { defineComponent, reactive, toRefs, PropType, watch } from 'vue';
import { StateModel, QuestionItemModel, OptionItemModel, EmitModel } from './types';

export default defineComponent({
	props: {
		list: {
			type: Array as PropType<QuestionItemModel[]>,
			default: () => []
		}
	},
	setup(props, { emit }: { emit: EmitModel }) {
		const state = reactive<StateModel>({
			// 选中的值
			selectList: []
		});

		const methods = {
			onSelectOption(index: number, option: OptionItemModel): void {
				state.selectList[index].optionIndexList = [option.index];
				emit('change', state.selectList);
				// console.log(8888, state.selectList);
			}
		};

		watch(
			() => props.list,
			newVal => {
				if (newVal?.length) {
					state.selectList = newVal.map(item => {
						return {
							questionCode: item.code,
							optionIndexList: []
						};
					});
				}
			}
		);

		return {
			...toRefs(state),
			...methods
		};
	}
});

<template>
	<div class="drive-license-components">
		<div class="tab-container">
			<div
				class="item {{activeTab === LicenseTypeEnum.CAR ? 'active' : ''}}"
				@click="onSwitch(LicenseTypeEnum.CAR)"
			>
				驾驶证
			</div>
			<div
				class="item {{activeTab === LicenseTypeEnum.UAV ? 'active' : ''}}"
				@click="onSwitch(LicenseTypeEnum.UAV)"
			>
				无人机
			</div>
		</div>

		<div class="w-full pl-15 pr-15 pb-10" v-if="activeTab === LicenseTypeEnum.CAR">
			<div v-for="(item, index) in LICENSE_OPTIONS" :key="index" class="flex flex-wrap justify-between">
				<div class="flex flex-100 mt-30 text-16">{{ item.groupName }}</div>
				<div v-for="(value, index) in item.list" :key="index" class="w-162 h-40 mt-15 leading-40 text-center">
					<div
						class="border-1 border-[rgba(238,238,238,1)] border-solid"
						:class="[value.carType === carType ? 'active' : '']"
						@click="onClick(value)"
					>
						<span class="text-16 mr-5">{{ value.carType }}</span>
						<span class="text-13">{{ value.carDesc }}</span>
					</div>
				</div>
			</div>
		</div>

		<div class="w-full pl-15 pr-15 pb-10" v-if="activeTab === LicenseTypeEnum.UAV">
			<div v-for="(item, index) in UAV_LICENSE_OPTIONS" :key="index" class="flex flex-wrap justify-between">
				<div class="flex flex-100 mt-30 text-16">{{ item.groupName }}</div>
				<div v-for="(value, index) in item.list" :key="index" class="w-162 h-40 mt-15 leading-40 text-center">
					<div
						class="border-1 border-[rgba(238,238,238,1)] border-solid"
						:class="[value.carType === uavType ? 'active' : '']"
						@click="onClickUav(item, value)"
					>
						<span class="text-13">{{ item.groupName }}（{{ value.carDesc }}）</span>
						<span class="tag" v-if="value.tag">{{ value.tag }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>

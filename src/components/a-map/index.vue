<template>
	<div class="overflow-hidden">
		<div class="h-50 flex justify-between items-center ml-15 mr-15 mt-5 mb-5">
			<div class="h-40 flex items-center p-10 bg-[rgba(242,242,242,1)] rounded-19 h-38 w-full">
				<div class="w-full flex items-center">
					<div class="text-14 text-[rgba(51,51,51,1)] mr-10 icon" @click="openSelectCity">
						{{ city.name }}
					</div>
					<div class="search-split"></div>
					<input
						class="w-full bg-transparent text-14 text-[rgba(160,160,160,1)] ml-10 searchInputId"
						type="text"
						v-model="searchKeywords"
						placeholder="小区/写字楼/学校 等"
						@input="onSearch"
						@focus="onFocus"
					/>
				</div>
			</div>
			<div class="text-14 text-[rgba(51,51,51,1)] w-38 ml-15" @click="onCancel" v-if="cancelVisible">取消</div>
		</div>

		<div v-if="handleToLocation">
			<div class="empty-location" >
				<div class="empty-location-item">
					<div class="empty-location-icon"></div>
					<p>定位失败</p>
					<div class="btn-list">
						<div class="empty-location-btn btn-list-left">去手动定位</div>
						<div class="empty-location-btn btn-list-right">开启定位</div>
					</div>
				</div>
			</div>
		</div>

		<div v-else class="map-container-box" ref="mapContainer"></div>

		<div v-if="cancelVisible" class="cancel-visible overflow-x-auto" :style="{ top: top + 'px' }">
			<div class="w-full overflow-hidden" v-if="mapSearchList.length > 0">
				<div
					class="pl-20 pr-20 pt-12"
					v-for="(item, index) in mapSearchList"
					:key="index"
					@click="goIndex(item)"
				>
					<div class="w-full flex items-center">
						<div :class="[index === 0 ? 'active' : 'no-active']" class="mr-11 flex-shrink-0"></div>
						<div class="border-b-1 border-[rgba(238,238,238,1)] border-solid w-full">
							<div
								class="text-16 w-11/12 truncate"
								:class="[index === 0 ? 'text-[rgba(29,172,249,1)]' : 'text-[rgba(51,51,51,1)]']"
							>
								{{ item.name }}
							</div>
							<div class="text-13 text-[rgba(153,153,153,1)] mt-2 w-11/12 truncate mb-12">
								{{ item.address }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 城市选择组件 -->
		<select-city-comp ref="selectCityCompRef" @updateCityCode="updateCityCode" />
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
